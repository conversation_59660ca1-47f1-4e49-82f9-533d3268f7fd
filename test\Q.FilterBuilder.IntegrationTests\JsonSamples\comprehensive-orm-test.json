{"condition": "AND", "rules": [{"field": "User.IsActive", "operator": "equal", "value": true, "type": "bool"}, {"field": "User.Age", "operator": "greater_or_equal", "value": 25, "type": "int"}], "groups": [{"condition": "OR", "rules": [{"field": "Product.Name", "operator": "contains", "value": "Premium", "type": "string"}, {"field": "Product.Price", "operator": "greater", "value": 100.0, "type": "decimal"}]}, {"condition": "AND", "rules": [{"field": "Category.Name", "operator": "in", "value": ["Electronics", "Software"], "type": "string"}, {"field": "Category.IsActive", "operator": "equal", "value": true, "type": "bool"}, {"field": "Product.IsAvailable", "operator": "equal", "value": true, "type": "bool"}, {"field": "Product.Stock", "operator": "greater", "value": 0, "type": "int"}]}]}