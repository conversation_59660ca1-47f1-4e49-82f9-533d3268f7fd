﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="../Q.FilterBuilder.Core/Q.FilterBuilder.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.5" />
    <PackageReference Include="Dapper" Version="2.1.35" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>netstandard2.1</TargetFramework>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
    <GeneratePackageOnBuild>True</GeneratePackageOnBuild>
    <AssemblyName>Q.FilterBuilder.MySql</AssemblyName>
    <RootNamespace>Q.FilterBuilder.MySql</RootNamespace>
    <PackageId>Q.FilterBuilder.MySql</PackageId>
  </PropertyGroup>

</Project>
