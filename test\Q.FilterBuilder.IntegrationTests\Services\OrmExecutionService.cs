using System.Data;
using System.Data.Common;
using Microsoft.EntityFrameworkCore;
using Microsoft.Data.SqlClient;
using MySqlConnector;
using Npgsql;
using Dapper;
using Q.FilterBuilder.IntegrationTests.Configuration;
using Q.FilterBuilder.IntegrationTests.Database;
using Q.FilterBuilder.IntegrationTests.Database.Models;
using Q.FilterBuilder.SqlServer.Extensions;
using Q.FilterBuilder.MySql.Extensions;
using Q.FilterBuilder.PostgreSql.Extensions;

namespace Q.FilterBuilder.IntegrationTests.Services;

/// <summary>
/// Provider-specific ORM execution service
/// </summary>
public class OrmExecutionService : IOrmExecutionService
{
    private readonly TestDbContext _context;
    private readonly TestConfiguration _testConfig;
    private readonly string _connectionString;

    public OrmExecutionService(TestDbContext context, TestConfiguration testConfig)
    {
        _context = context;
        _testConfig = testConfig;
        _connectionString = _context.Database.GetConnectionString()!;
    }

    public async Task<List<User>> ExecuteWithEntityFrameworkAsync(string whereClause, object[] parameters)
    {
        var provider = _testConfig.GetDatabaseProvider();
        var tableName = GetTableName(provider.ToString());

        var convertedQuery = provider switch
        {
            DatabaseProvider.SqlServer => SqlServerOrmExtensions.ToEfRawQueryFormat(whereClause),
            DatabaseProvider.MySql => MySqlOrmExtensions.ConvertQueryForEntityFramework(whereClause, parameters.Length),
            DatabaseProvider.PostgreSql => PostgreSqlOrmExtensions.ConvertQueryForEntityFramework(whereClause, parameters.Length),
            _ => throw new ArgumentException($"Unsupported provider: {provider}")
        };

        var sql = $"SELECT * FROM {tableName} WHERE {convertedQuery}";
        return await _context.Set<User>().FromSqlRaw(sql, parameters).ToListAsync();
    }

    public async Task<List<dynamic>> ExecuteWithDapperAsync(string whereClause, object[] parameters)
    {
        var provider = _testConfig.GetDatabaseProvider();
        var tableName = GetTableName(provider.ToString());
        var sql = $"SELECT * FROM {tableName} WHERE {whereClause}";

        return provider switch
        {
            DatabaseProvider.SqlServer => await ExecuteDapperSqlServerAsync(sql, parameters),
            DatabaseProvider.MySql => await ExecuteDapperMySqlAsync(sql, parameters),
            DatabaseProvider.PostgreSql => await ExecuteDapperPostgreSqlAsync(sql, parameters),
            _ => throw new ArgumentException($"Unsupported provider: {provider}")
        };
    }

    public async Task<List<dynamic>> ExecuteWithAdoNetAsync(string whereClause, object[] parameters)
    {
        var provider = _testConfig.GetDatabaseProvider();
        var tableName = GetTableName(provider.ToString());
        var sql = $"SELECT * FROM {tableName} WHERE {whereClause}";

        return provider switch
        {
            DatabaseProvider.SqlServer => await ExecuteAdoNetSqlServerAsync(sql, parameters),
            DatabaseProvider.MySql => await ExecuteAdoNetMySqlAsync(sql, parameters),
            DatabaseProvider.PostgreSql => await ExecuteAdoNetPostgreSqlAsync(sql, parameters),
            _ => throw new ArgumentException($"Unsupported provider: {provider}")
        };
    }

    private string GetTableName(string tableName)
    {
        var provider = _testConfig.GetDatabaseProvider();
        return provider switch
        {
            DatabaseProvider.PostgreSql => $"\"{tableName}\"", // PostgreSQL needs quoted table names for case sensitivity
            DatabaseProvider.MySql => $"`{tableName}`", // MySQL uses backticks
            DatabaseProvider.SqlServer => $"[{tableName}]", // SQL Server uses square brackets
            _ => tableName
        };
    }

    private async Task<List<dynamic>> ExecuteDapperSqlServerAsync(string sql, object[] parameters)
    {
        using var connection = new SqlConnection(_connectionString);
        var paramDict = SqlServerOrmExtensions.ToDapperParameters(parameters);
        var results = await connection.QueryAsync(sql, paramDict);
        return results.ToList();
    }

    private async Task<List<dynamic>> ExecuteDapperMySqlAsync(string sql, object[] parameters)
    {
        using var connection = new MySqlConnection(_connectionString);
        var dynamicParams = MySqlOrmExtensions.ConvertParametersForDapper(parameters);
        var results = await connection.QueryAsync(sql, dynamicParams);
        return results.ToList();
    }

    private async Task<List<dynamic>> ExecuteDapperPostgreSqlAsync(string sql, object[] parameters)
    {
        using var connection = new NpgsqlConnection(_connectionString);
        var paramDict = PostgreSqlOrmExtensions.ConvertParametersForDapper(sql, parameters);
        var results = await connection.QueryAsync(sql, paramDict);
        return results.ToList();
    }

    private async Task<List<dynamic>> ExecuteAdoNetSqlServerAsync(string sql, object[] parameters)
    {
        using var connection = new SqlConnection(_connectionString);
        using var command = connection.CreateCommand();
        command.CommandText = sql;

        for (var i = 0; i < parameters.Length; i++)
        {
            var parameter = command.CreateParameter();
            parameter.ParameterName = SqlServerOrmExtensions.GetParameterName(i);
            parameter.Value = parameters[i] ?? DBNull.Value;
            command.Parameters.Add(parameter);
        }

        await connection.OpenAsync();
        using var reader = await command.ExecuteReaderAsync();
        return await ReadResultsAsync(reader);
    }

    private async Task<List<dynamic>> ExecuteAdoNetMySqlAsync(string sql, object[] parameters)
    {
        using var connection = new MySqlConnection(_connectionString);
        using var command = connection.CreateCommand();
        command.CommandText = sql;

        for (var i = 0; i < parameters.Length; i++)
        {
            var parameter = command.CreateParameter();
            parameter.Value = parameters[i] ?? DBNull.Value;
            command.Parameters.Add(parameter);
        }

        await connection.OpenAsync();
        using var reader = await command.ExecuteReaderAsync();
        return await ReadResultsAsync(reader);
    }

    private async Task<List<dynamic>> ExecuteAdoNetPostgreSqlAsync(string sql, object[] parameters)
    {
        using var connection = new NpgsqlConnection(_connectionString);
        using var command = connection.CreateCommand();
        command.CommandText = sql;

        for (var i = 0; i < parameters.Length; i++)
        {
            var parameter = command.CreateParameter();
            parameter.ParameterName = PostgreSqlOrmExtensions.GetParameterName(i);
            parameter.Value = parameters[i] ?? DBNull.Value;
            command.Parameters.Add(parameter);
        }

        await connection.OpenAsync();
        using var reader = await command.ExecuteReaderAsync();
        return await ReadResultsAsync(reader);
    }

    private static async Task<List<dynamic>> ReadResultsAsync(DbDataReader reader)
    {
        var results = new List<dynamic>();
        while (await reader.ReadAsync())
        {
            var row = new Dictionary<string, object?>();
            for (var i = 0; i < reader.FieldCount; i++)
            {
                row[reader.GetName(i)] = reader.IsDBNull(i) ? null : reader.GetValue(i);
            }
            results.Add(row);
        }
        return results;
    }
}
