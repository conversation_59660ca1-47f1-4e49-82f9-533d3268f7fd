using Microsoft.EntityFrameworkCore;
using Q.FilterBuilder.IntegrationTests.Configuration;
using Q.FilterBuilder.IntegrationTests.Database;
using Q.FilterBuilder.IntegrationTests.Database.Models;
using Q.FilterBuilder.SqlServer.Extensions;
using Q.FilterBuilder.MySql.Extensions;
using Q.FilterBuilder.PostgreSql.Extensions;

namespace Q.FilterBuilder.IntegrationTests.Services;

/// <summary>
/// Provider-specific ORM execution service
/// </summary>
public class OrmExecutionService : IOrmExecutionService
{
    private readonly TestDbContext _context;
    private readonly TestConfiguration _testConfig;
    private readonly string _connectionString;

    public OrmExecutionService(TestDbContext context, TestConfiguration testConfig)
    {
        _context = context;
        _testConfig = testConfig;
        _connectionString = _context.Database.GetConnectionString()!;
    }

    public async Task<List<User>> ExecuteWithEntityFrameworkAsync(string whereClause, object[] parameters)
    {
        var provider = _testConfig.GetDatabaseProvider();
        
        return provider switch
        {
            DatabaseProvider.SqlServer => await _context.ExecuteWithEntityFrameworkAsync<User>(whereClause, parameters),
            DatabaseProvider.MySql => await _context.ExecuteWithEntityFrameworkAsync<User>(whereClause, parameters),
            DatabaseProvider.PostgreSql => await _context.ExecuteWithEntityFrameworkAsync<User>(whereClause, parameters),
            _ => throw new ArgumentException($"Unsupported provider: {provider}")
        };
    }

    public async Task<List<dynamic>> ExecuteWithDapperAsync(string whereClause, object[] parameters)
    {
        var provider = _testConfig.GetDatabaseProvider();
        
        return provider switch
        {
            DatabaseProvider.SqlServer => await SqlServerOrmExtensions.ExecuteWithDapperAsync<dynamic>(_connectionString, whereClause, parameters),
            DatabaseProvider.MySql => await MySqlOrmExtensions.ExecuteWithDapperAsync<dynamic>(_connectionString, whereClause, parameters),
            DatabaseProvider.PostgreSql => await PostgreSqlOrmExtensions.ExecuteWithDapperAsync<dynamic>(_connectionString, whereClause, parameters),
            _ => throw new ArgumentException($"Unsupported provider: {provider}")
        };
    }

    public async Task<List<dynamic>> ExecuteWithAdoNetAsync(string whereClause, object[] parameters)
    {
        var provider = _testConfig.GetDatabaseProvider();
        
        return provider switch
        {
            DatabaseProvider.SqlServer => await SqlServerOrmExtensions.ExecuteWithAdoNetAsync(_connectionString, whereClause, parameters),
            DatabaseProvider.MySql => await MySqlOrmExtensions.ExecuteWithAdoNetAsync(_connectionString, whereClause, parameters),
            DatabaseProvider.PostgreSql => await PostgreSqlOrmExtensions.ExecuteWithAdoNetAsync(_connectionString, whereClause, parameters),
            _ => throw new ArgumentException($"Unsupported provider: {provider}")
        };
    }

    public string GetExpectedSqlQuery(string whereClause, string tableName = "Users")
    {
        var provider = _testConfig.GetDatabaseProvider();
        
        return provider switch
        {
            DatabaseProvider.SqlServer => SqlServerOrmExtensions.GetExpectedSqlQuery(whereClause, tableName),
            DatabaseProvider.MySql => MySqlOrmExtensions.GetExpectedSqlQuery(whereClause, tableName),
            DatabaseProvider.PostgreSql => PostgreSqlOrmExtensions.GetExpectedSqlQuery(whereClause, tableName),
            _ => throw new ArgumentException($"Unsupported provider: {provider}")
        };
    }

    public bool ValidateParameterFormat(string sql)
    {
        var provider = _testConfig.GetDatabaseProvider();
        
        return provider switch
        {
            DatabaseProvider.SqlServer => SqlServerOrmExtensions.ValidateParameterFormat(sql),
            DatabaseProvider.MySql => MySqlOrmExtensions.ValidateParameterFormat(sql),
            DatabaseProvider.PostgreSql => PostgreSqlOrmExtensions.ValidateParameterFormat(sql),
            _ => throw new ArgumentException($"Unsupported provider: {provider}")
        };
    }
}
