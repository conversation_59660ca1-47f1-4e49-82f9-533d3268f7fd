using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace Q.FilterBuilder.PostgreSql.Extensions;

/// <summary>
/// PostgreSQL specific parameter and query transformation extensions
/// </summary>
public static class PostgreSqlOrmExtensions
{
    /// <summary>
    /// Convert PostgreSQL query parameters for Entity Framework Core
    /// EF Core uses {0}, {1}, etc. instead of $1, $2, etc.
    /// </summary>
    public static string ConvertQueryForEntityFramework(this string whereClause, int parameterCount)
    {
        var efQuery = whereClause;
        for (var i = 1; i <= parameterCount; i++)
        {
            efQuery = efQuery.Replace($"${i}", $"{{{i - 1}}}");
        }
        return efQuery;
    }

    /// <summary>
    /// Convert PostgreSQL query parameters for Dapper
    /// PostgreSQL uses $1, $2, etc. but Dapper needs named parameters
    /// </summary>
    public static Dictionary<string, object> ConvertParametersForDapper(this string whereClause, object[] parameters)
    {
        var paramDict = new Dictionary<string, object>();

        // Find all PostgreSQL parameters in the SQL and replace them
        var pgParamPattern = @"\$(\d+)";
        var matches = Regex.Matches(whereClause, pgParamPattern);

        // Create a mapping of PostgreSQL parameter indices to actual parameter array indices
        var paramMapping = new Dictionary<int, int>();
        for (var i = 0; i < matches.Count; i++)
        {
            var match = matches[i];
            var pgParamIndex = int.Parse(match.Groups[1].Value);
            if (!paramMapping.ContainsKey(pgParamIndex))
            {
                paramMapping[pgParamIndex] = i; // Map to sequential parameter array index
            }
        }

        // Create Dapper parameters
        foreach (var kvp in paramMapping)
        {
            var dapperParam = $"p{kvp.Value}";
            if (kvp.Value < parameters.Length)
            {
                paramDict[dapperParam] = parameters[kvp.Value];
            }
        }

        return paramDict;
    }

    /// <summary>
    /// Get PostgreSQL table name with proper quoting
    /// </summary>
    public static string GetPostgreSqlTableName(this string tableName)
    {
        return $"\"{tableName}\"";
    }

    /// <summary>
    /// Get expected SQL query string for PostgreSQL
    /// </summary>
    public static string GetExpectedSqlQuery(this string whereClause, string tableName = "Users")
    {
        return $"SELECT * FROM \"{tableName}\" WHERE {whereClause}";
    }

    /// <summary>
    /// Validate PostgreSQL parameter format
    /// </summary>
    public static bool ValidateParameterFormat(this string sql)
    {
        // PostgreSQL uses $1, $2, etc.
        return Regex.IsMatch(sql, @"\$\d+");
    }

    /// <summary>
    /// Get PostgreSQL parameter name for ADO.NET
    /// </summary>
    public static string GetParameterName(this int index)
    {
        return $"${index + 1}"; // PostgreSQL uses 1-based indexing
    }
}
