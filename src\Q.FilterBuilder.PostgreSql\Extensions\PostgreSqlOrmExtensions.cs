using System.Data;
using System.Data.Common;
using Npgsql;
using Microsoft.EntityFrameworkCore;
using Dapper;
using System.Text.RegularExpressions;

namespace Q.FilterBuilder.PostgreSql.Extensions;

/// <summary>
/// PostgreSQL specific ORM integration extensions
/// </summary>
public static class PostgreSqlOrmExtensions
{
    /// <summary>
    /// Execute query using Entity Framework Core with PostgreSQL specific parameter handling
    /// </summary>
    public static async Task<List<T>> ExecuteWithEntityFrameworkAsync<T>(
        this DbContext context, 
        string whereClause, 
        object[] parameters) where T : class
    {
        var tableName = GetTableName<T>();
        var sql = $"SELECT * FROM \"{tableName}\" WHERE {whereClause}";
        
        // Convert parameters for EF Core (uses {0}, {1}, etc.)
        var efSql = ConvertParametersForEntityFramework(sql, parameters.Length);
        
        return await context.Set<T>().FromSqlRaw(efSql, parameters).ToListAsync();
    }

    /// <summary>
    /// Execute query using Dapper with PostgreSQL specific parameter handling
    /// </summary>
    public static async Task<List<T>> ExecuteWithDapperAsync<T>(
        string connectionString,
        string whereClause, 
        object[] parameters)
    {
        var tableName = GetTableName<T>();
        var sql = $"SELECT * FROM \"{tableName}\" WHERE {whereClause}";
        
        using var connection = new NpgsqlConnection(connectionString);
        var paramDict = ConvertParametersForDapper(sql, parameters);
        
        var results = await connection.QueryAsync<T>(sql, paramDict);
        return results.ToList();
    }

    /// <summary>
    /// Execute query using ADO.NET with PostgreSQL specific parameter handling
    /// </summary>
    public static async Task<List<dynamic>> ExecuteWithAdoNetAsync(
        string connectionString,
        string whereClause, 
        object[] parameters)
    {
        var tableName = GetTableName<dynamic>();
        var sql = $"SELECT * FROM \"Users\" WHERE {whereClause}";
        
        using var connection = new NpgsqlConnection(connectionString);
        using var command = connection.CreateCommand();
        command.CommandText = sql;
        
        // Add parameters with PostgreSQL naming ($1, $2, etc.)
        for (int i = 0; i < parameters.Length; i++)
        {
            var parameter = command.CreateParameter();
            parameter.ParameterName = $"${i + 1}";
            parameter.Value = parameters[i] ?? DBNull.Value;
            command.Parameters.Add(parameter);
        }

        await connection.OpenAsync();
        using var reader = await command.ExecuteReaderAsync();
        return await ReadResultsAsync(reader);
    }

    /// <summary>
    /// Get expected SQL query string for PostgreSQL
    /// </summary>
    public static string GetExpectedSqlQuery(string whereClause, string tableName = "Users")
    {
        return $"SELECT * FROM \"{tableName}\" WHERE {whereClause}";
    }

    /// <summary>
    /// Validate PostgreSQL parameter format
    /// </summary>
    public static bool ValidateParameterFormat(string sql)
    {
        // PostgreSQL uses $1, $2, etc.
        return Regex.IsMatch(sql, @"\$\d+");
    }

    private static string GetTableName<T>()
    {
        var type = typeof(T);
        if (type == typeof(dynamic))
            return "Users"; // Default table for dynamic queries
        
        var tableName = type.Name;
        if (tableName.EndsWith("y"))
            tableName = tableName.Substring(0, tableName.Length - 1) + "ies";
        else if (!tableName.EndsWith("s"))
            tableName += "s";
        
        return tableName;
    }

    private static string ConvertParametersForEntityFramework(string sql, int parameterCount)
    {
        var efSql = sql;
        for (int i = 1; i <= parameterCount; i++)
        {
            efSql = efSql.Replace($"${i}", $"{{{i - 1}}}");
        }
        return efSql;
    }

    private static Dictionary<string, object> ConvertParametersForDapper(string sql, object[] parameters)
    {
        var paramDict = new Dictionary<string, object>();
        
        // Find all PostgreSQL parameters in the SQL and replace them
        var pgParamPattern = @"\$(\d+)";
        var matches = Regex.Matches(sql, pgParamPattern);
        
        // Create a mapping of PostgreSQL parameter indices to actual parameter array indices
        var paramMapping = new Dictionary<int, int>();
        for (int i = 0; i < matches.Count; i++)
        {
            var match = matches[i];
            var pgParamIndex = int.Parse(match.Groups[1].Value);
            if (!paramMapping.ContainsKey(pgParamIndex))
            {
                paramMapping[pgParamIndex] = i; // Map to sequential parameter array index
            }
        }

        // Create Dapper parameters
        foreach (var kvp in paramMapping)
        {
            var dapperParam = $"p{kvp.Value}";
            if (kvp.Value < parameters.Length)
            {
                paramDict[dapperParam] = parameters[kvp.Value];
            }
        }

        return paramDict;
    }

    private static async Task<List<dynamic>> ReadResultsAsync(DbDataReader reader)
    {
        var results = new List<dynamic>();
        while (await reader.ReadAsync())
        {
            var row = new Dictionary<string, object?>();
            for (int i = 0; i < reader.FieldCount; i++)
            {
                row[reader.GetName(i)] = reader.IsDBNull(i) ? null : reader.GetValue(i);
            }
            results.Add(row);
        }
        return results;
    }
}
