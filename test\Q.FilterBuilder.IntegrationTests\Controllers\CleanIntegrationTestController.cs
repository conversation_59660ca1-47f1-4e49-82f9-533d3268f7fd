using Microsoft.AspNetCore.Mvc;
using Q.FilterBuilder.Core;
using Q.FilterBuilder.Core.Models;
using Q.FilterBuilder.IntegrationTests.Configuration;
using Q.FilterBuilder.IntegrationTests.Infrastructure;
using Q.FilterBuilder.IntegrationTests.Services;

namespace Q.FilterBuilder.IntegrationTests.Controllers;

/// <summary>
/// Clean, refactored controller for integration testing of FilterBuilder functionality
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class CleanIntegrationTestController : ControllerBase
{
    private readonly IFilterBuilder _filterBuilder;
    private readonly IOrmExecutionService _ormService;
    private readonly TestConfiguration _testConfig;

    public CleanIntegrationTestController(
        IFilterBuilder filterBuilder,
        IOrmExecutionService ormService,
        TestConfiguration testConfig)
    {
        _filterBuilder = filterBuilder;
        _ormService = ormService;
        _testConfig = testConfig;
    }

    /// <summary>
    /// Health check endpoint
    /// </summary>
    [HttpGet("health")]
    public IActionResult Health()
    {
        return Ok(new { Status = "Healthy", Provider = _testConfig.GetDatabaseProvider().ToString() });
    }

    /// <summary>
    /// Builds a query from FilterGroup without executing it
    /// </summary>
    [HttpPost("build-query")]
    public IActionResult BuildQuery([FromBody] FilterGroup filterGroup)
    {
        try
        {
            var (query, parameters) = _filterBuilder.Build(filterGroup);
            var expectedSql = _ormService.GetExpectedSqlQuery(query);
            
            return Ok(new QueryResult 
            { 
                Query = expectedSql, 
                Parameters = parameters,
                WhereClause = query,
                IsValidParameterFormat = _ormService.ValidateParameterFormat(query)
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message });
        }
    }

    /// <summary>
    /// Execute filter using Entity Framework Core
    /// </summary>
    [HttpPost("execute-efcore-users")]
    public async Task<IActionResult> ExecuteEFCoreUsers([FromBody] FilterGroup filterGroup)
    {
        try
        {
            var (query, parameters) = _filterBuilder.Build(filterGroup);
            var results = await _ormService.ExecuteWithEntityFrameworkAsync(query, parameters);
            var expectedSql = _ormService.GetExpectedSqlQuery(query);

            return Ok(new
            {
                Query = expectedSql,
                WhereClause = query,
                Parameters = parameters,
                Results = results,
                Count = results.Count,
                ORM = "EntityFramework"
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message, StackTrace = ex.StackTrace });
        }
    }

    /// <summary>
    /// Execute filter using Dapper
    /// </summary>
    [HttpPost("execute-dapper-users")]
    public async Task<IActionResult> ExecuteDapperUsers([FromBody] FilterGroup filterGroup)
    {
        try
        {
            var (query, parameters) = _filterBuilder.Build(filterGroup);
            var results = await _ormService.ExecuteWithDapperAsync(query, parameters);
            var expectedSql = _ormService.GetExpectedSqlQuery(query);

            return Ok(new
            {
                Query = expectedSql,
                WhereClause = query,
                Parameters = parameters,
                Results = results,
                Count = results.Count,
                ORM = "Dapper"
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message, StackTrace = ex.StackTrace });
        }
    }

    /// <summary>
    /// Execute filter using ADO.NET
    /// </summary>
    [HttpPost("execute-adonet-users")]
    public async Task<IActionResult> ExecuteAdoNetUsers([FromBody] FilterGroup filterGroup)
    {
        try
        {
            var (query, parameters) = _filterBuilder.Build(filterGroup);
            var results = await _ormService.ExecuteWithAdoNetAsync(query, parameters);
            var expectedSql = _ormService.GetExpectedSqlQuery(query);

            return Ok(new
            {
                Query = expectedSql,
                WhereClause = query,
                Parameters = parameters,
                Results = results,
                Count = results.Count,
                ORM = "ADO.NET"
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message, StackTrace = ex.StackTrace });
        }
    }

    /// <summary>
    /// Execute filter for general users table operations
    /// </summary>
    [HttpPost("execute-users-filter")]
    public async Task<IActionResult> ExecuteUsersFilter([FromBody] FilterGroup filterGroup)
    {
        try
        {
            var (query, parameters) = _filterBuilder.Build(filterGroup);
            var results = await _ormService.ExecuteWithDapperAsync(query, parameters);
            var expectedSql = _ormService.GetExpectedSqlQuery(query);

            return Ok(new
            {
                Query = expectedSql,
                WhereClause = query,
                Parameters = parameters,
                Results = results,
                Count = results.Count
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message, StackTrace = ex.StackTrace });
        }
    }

    /// <summary>
    /// Execute comprehensive filter scenarios
    /// </summary>
    [HttpPost("execute-comprehensive-filter")]
    public async Task<IActionResult> ExecuteComprehensiveFilter([FromBody] FilterGroup filterGroup)
    {
        try
        {
            var (query, parameters) = _filterBuilder.Build(filterGroup);

            var efResults = await _ormService.ExecuteWithEntityFrameworkAsync(query, parameters);
            var dapperResults = await _ormService.ExecuteWithDapperAsync(query, parameters);
            var adoNetResults = await _ormService.ExecuteWithAdoNetAsync(query, parameters);
            var expectedSql = _ormService.GetExpectedSqlQuery(query);

            return Ok(new
            {
                Query = expectedSql,
                WhereClause = query,
                Parameters = parameters,
                Results = new
                {
                    EntityFramework = efResults,
                    Dapper = dapperResults,
                    AdoNet = adoNetResults
                },
                Counts = new
                {
                    EntityFramework = efResults.Count,
                    Dapper = dapperResults.Count,
                    AdoNet = adoNetResults.Count
                }
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message, StackTrace = ex.StackTrace });
        }
    }
}

/// <summary>
/// Enhanced result of query building operation
/// </summary>
public class QueryResult
{
    public string Query { get; set; } = string.Empty;
    public string WhereClause { get; set; } = string.Empty;
    public object[] Parameters { get; set; } = Array.Empty<object>();
    public bool IsValidParameterFormat { get; set; }
}
