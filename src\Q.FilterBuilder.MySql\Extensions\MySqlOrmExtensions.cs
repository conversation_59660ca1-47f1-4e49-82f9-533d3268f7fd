using System.Data;
using System.Data.Common;
using MySqlConnector;
using Microsoft.EntityFrameworkCore;
using Dapper;

namespace Q.FilterBuilder.MySql.Extensions;

/// <summary>
/// MySQL specific ORM integration extensions
/// </summary>
public static class MySqlOrmExtensions
{
    /// <summary>
    /// Execute query using Entity Framework Core with MySQL specific parameter handling
    /// </summary>
    public static async Task<List<T>> ExecuteWithEntityFrameworkAsync<T>(
        this DbContext context, 
        string whereClause, 
        object[] parameters) where T : class
    {
        var tableName = GetTableName<T>();
        var sql = $"SELECT * FROM `{tableName}` WHERE {whereClause}";
        
        // Convert parameters for EF Core (uses {0}, {1}, etc.)
        var efSql = ConvertParametersForEntityFramework(sql, parameters.Length);
        
        return await context.Set<T>().FromSqlRaw(efSql, parameters).ToListAsync();
    }

    /// <summary>
    /// Execute query using Dapper with MySQL specific parameter handling
    /// </summary>
    public static async Task<List<T>> ExecuteWithDapperAsync<T>(
        string connectionString,
        string whereClause, 
        object[] parameters)
    {
        var tableName = GetTableName<T>();
        var sql = $"SELECT * FROM `{tableName}` WHERE {whereClause}";
        
        using var connection = new MySqlConnection(connectionString);
        
        // MySQL uses positional parameters (?)
        var dynamicParams = new DynamicParameters();
        for (int i = 0; i < parameters.Length; i++)
        {
            dynamicParams.Add($"p{i}", parameters[i]);
        }
        
        var results = await connection.QueryAsync<T>(sql, dynamicParams);
        return results.ToList();
    }

    /// <summary>
    /// Execute query using ADO.NET with MySQL specific parameter handling
    /// </summary>
    public static async Task<List<dynamic>> ExecuteWithAdoNetAsync(
        string connectionString,
        string whereClause, 
        object[] parameters)
    {
        var tableName = GetTableName<dynamic>();
        var sql = $"SELECT * FROM `Users` WHERE {whereClause}";
        
        using var connection = new MySqlConnection(connectionString);
        using var command = connection.CreateCommand();
        command.CommandText = sql;
        
        // Add parameters with MySQL naming (positional ?)
        for (int i = 0; i < parameters.Length; i++)
        {
            var parameter = command.CreateParameter();
            parameter.Value = parameters[i] ?? DBNull.Value;
            command.Parameters.Add(parameter);
        }

        await connection.OpenAsync();
        using var reader = await command.ExecuteReaderAsync();
        return await ReadResultsAsync(reader);
    }

    /// <summary>
    /// Get expected SQL query string for MySQL
    /// </summary>
    public static string GetExpectedSqlQuery(string whereClause, string tableName = "Users")
    {
        return $"SELECT * FROM `{tableName}` WHERE {whereClause}";
    }

    /// <summary>
    /// Validate MySQL parameter format
    /// </summary>
    public static bool ValidateParameterFormat(string sql)
    {
        // MySQL uses ? for parameters
        return sql.Contains("?");
    }

    private static string GetTableName<T>()
    {
        var type = typeof(T);
        if (type == typeof(dynamic))
            return "Users"; // Default table for dynamic queries
        
        var tableName = type.Name;
        if (tableName.EndsWith("y"))
            tableName = tableName.Substring(0, tableName.Length - 1) + "ies";
        else if (!tableName.EndsWith("s"))
            tableName += "s";
        
        return tableName;
    }

    private static string ConvertParametersForEntityFramework(string sql, int parameterCount)
    {
        var efSql = sql;
        // MySQL EF Core uses ? parameters, convert to {0}, {1}, etc.
        var paramIndex = 0;
        while (efSql.Contains("?") && paramIndex < parameterCount)
        {
            var index = efSql.IndexOf("?");
            if (index >= 0)
            {
                efSql = efSql.Substring(0, index) + $"{{{paramIndex}}}" + efSql.Substring(index + 1);
                paramIndex++;
            }
        }
        return efSql;
    }

    private static async Task<List<dynamic>> ReadResultsAsync(DbDataReader reader)
    {
        var results = new List<dynamic>();
        while (await reader.ReadAsync())
        {
            var row = new Dictionary<string, object?>();
            for (int i = 0; i < reader.FieldCount; i++)
            {
                row[reader.GetName(i)] = reader.IsDBNull(i) ? null : reader.GetValue(i);
            }
            results.Add(row);
        }
        return results;
    }
}
