using System.Collections.Generic;
using <PERSON>pper;

namespace Q.FilterBuilder.MySql.Extensions;

/// <summary>
/// MySQL specific parameter and query transformation extensions
/// </summary>
public static class MySqlOrmExtensions
{
    /// <summary>
    /// Convert MySQL query parameters for Entity Framework Core
    /// EF Core uses {0}, {1}, etc. instead of ? parameters
    /// </summary>
    public static string ConvertQueryForEntityFramework(this string whereClause, int parameterCount)
    {
        var efQuery = whereClause;
        // MySQL EF Core uses ? parameters, convert to {0}, {1}, etc.
        var paramIndex = 0;
        while (efQuery.Contains("?") && paramIndex < parameterCount)
        {
            var index = efQuery.IndexOf("?");
            if (index >= 0)
            {
                efQuery = efQuery.Substring(0, index) + $"{{{paramIndex}}}" + efQuery.Substring(index + 1);
                paramIndex++;
            }
        }
        return efQuery;
    }

    /// <summary>
    /// Convert MySQL query parameters for Dapper
    /// MySQL uses positional parameters with DynamicParameters
    /// </summary>
    public static DynamicParameters ConvertParametersForDapper(this object[] parameters)
    {
        var dynamicParams = new DynamicParameters();
        for (var i = 0; i < parameters.Length; i++)
        {
            dynamicParams.Add($"p{i}", parameters[i]);
        }
        return dynamicParams;
    }

    /// <summary>
    /// Get MySQL table name with proper quoting
    /// </summary>
    public static string GetMySqlTableName(this string tableName)
    {
        return $"`{tableName}`";
    }

    /// <summary>
    /// Get expected SQL query string for MySQL
    /// </summary>
    public static string GetExpectedSqlQuery(this string whereClause, string tableName = "Users")
    {
        return $"SELECT * FROM `{tableName}` WHERE {whereClause}";
    }

    /// <summary>
    /// Validate MySQL parameter format
    /// </summary>
    public static bool ValidateParameterFormat(this string sql)
    {
        // MySQL uses ? for parameters
        return sql.Contains("?");
    }

    /// <summary>
    /// Get MySQL parameter name for ADO.NET (positional)
    /// </summary>
    public static string GetParameterName(this int index)
    {
        return "?"; // MySQL uses positional parameters
    }
}
