using Microsoft.AspNetCore.Mvc;
using Q.FilterBuilder.Core;
using Q.FilterBuilder.Core.Models;
using Q.FilterBuilder.IntegrationTests.Configuration;
using Q.FilterBuilder.IntegrationTests.Services;

namespace Q.FilterBuilder.IntegrationTests.Controllers;

/// <summary>
/// controller for integration testing of FilterBuilder functionality
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class IntegrationTestController : ControllerBase
{
    private readonly IFilterBuilder _filterBuilder;
    private readonly IOrmExecutionService _ormService;
    private readonly TestConfiguration _testConfig;

    public IntegrationTestController(
        IFilterBuilder filterBuilder,
        IOrmExecutionService ormService,
        TestConfiguration testConfig)
    {
        _filterBuilder = filterBuilder;
        _ormService = ormService;
        _testConfig = testConfig;
    }

    /// <summary>
    /// Execute filter using Entity Framework Core
    /// </summary>
    [HttpPost("execute-efcore-users")]
    public async Task<IActionResult> ExecuteEFCoreUsers([FromBody] FilterGroup filterGroup)
    {
        try
        {
            var (query, parameters) = _filterBuilder.Build(filterGroup);
            var results = await _ormService.ExecuteWithEntityFrameworkAsync(query, parameters);

            return Ok(new
            {
                WhereClause = query,
                Parameters = parameters,
                Results = results,
                results.Count,
                ORM = "EntityFramework"
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message, ex.StackTrace });
        }
    }

    /// <summary>
    /// Execute filter using Dapper
    /// </summary>
    [HttpPost("execute-dapper-users")]
    public async Task<IActionResult> ExecuteDapperUsers([FromBody] FilterGroup filterGroup)
    {
        try
        {
            var (query, parameters) = _filterBuilder.Build(filterGroup);
            var results = await _ormService.ExecuteWithDapperAsync(query, parameters);

            return Ok(new
            {
                WhereClause = query,
                Parameters = parameters,
                Results = results,
                results.Count,
                ORM = "Dapper"
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message, ex.StackTrace });
        }
    }

    /// <summary>
    /// Execute filter using ADO.NET
    /// </summary>
    [HttpPost("execute-adonet-users")]
    public async Task<IActionResult> ExecuteAdoNetUsers([FromBody] FilterGroup filterGroup)
    {
        try
        {
            var (query, parameters) = _filterBuilder.Build(filterGroup);
            var results = await _ormService.ExecuteWithAdoNetAsync(query, parameters);

            return Ok(new
            {
                WhereClause = query,
                Parameters = parameters,
                Results = results,
                results.Count,
                ORM = "ADO.NET"
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message, ex.StackTrace });
        }
    }

    /// <summary>
    /// Execute filter for general users table operations
    /// </summary>
    [HttpPost("execute-users-filter")]
    public async Task<IActionResult> ExecuteUsersFilter([FromBody] FilterGroup filterGroup)
    {
        try
        {
            var (query, parameters) = _filterBuilder.Build(filterGroup);
            var results = await _ormService.ExecuteWithDapperAsync(query, parameters);

            return Ok(new
            {
                WhereClause = query,
                Parameters = parameters,
                Results = results,
                results.Count
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message, ex.StackTrace });
        }
    }
}
