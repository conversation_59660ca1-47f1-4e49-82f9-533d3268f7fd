using System.Data;
using System.Data.Common;
using System.Text.RegularExpressions;
using Dapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using MySqlConnector;
using Npgsql;
using Q.FilterBuilder.Core;
using Q.FilterBuilder.Core.Models;
using Q.FilterBuilder.IntegrationTests.Configuration;
using Q.FilterBuilder.IntegrationTests.Database;
using Q.FilterBuilder.IntegrationTests.Database.Models;
using Q.FilterBuilder.IntegrationTests.Infrastructure;

namespace Q.FilterBuilder.IntegrationTests.Controllers;

/// <summary>
/// Controller for integration testing of FilterBuilder functionality
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class IntegrationTestController : ControllerBase
{
    private readonly IFilterBuilder _filterBuilder;
    private readonly TestDbContext _context;
    private readonly TestConfiguration _testConfig;

    public IntegrationTestController(
        IFilterBuilder filterBuilder, 
        TestDbContext context,
        TestConfiguration testConfig)
    {
        _filterBuilder = filterBuilder;
        _context = context;
        _testConfig = testConfig;
    }

    /// <summary>
    /// Health check endpoint
    /// </summary>
    [HttpGet("health")]
    public IActionResult Health()
    {
        return Ok(new { Status = "Healthy", Provider = _testConfig.GetDatabaseProvider().ToString() });
    }

    /// <summary>
    /// Builds a query from FilterGroup without executing it
    /// </summary>
    [HttpPost("build-query")]
    public IActionResult BuildQuery([FromBody] FilterGroup filterGroup)
    {
        try
        {
            var (query, parameters) = _filterBuilder.Build(filterGroup);
            return Ok(new QueryResult { Query = query, Parameters = parameters });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message });
        }
    }

    /// <summary>
    /// Executes filter specifically for Users table
    /// </summary>
    [HttpPost("execute-users-filter")]
    public async Task<IActionResult> ExecuteUsersFilter([FromBody] FilterGroup filterGroup)
    {
        try
        {
            var (query, parameters) = _filterBuilder.Build(filterGroup);
            var tableName = GetTableName("Users");
            var sql = $"SELECT * FROM {tableName} WHERE {query}";

            var results = await ExecuteQueryAsync<User>(sql, parameters);

            return Ok(new
            {
                Query = sql,
                Parameters = parameters,
                Results = results,
                Count = results.Count
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message, StackTrace = ex.StackTrace, InnerException = ex.InnerException?.Message });
        }
    }

    /// <summary>
    /// Executes filter specifically for Products table
    /// </summary>
    [HttpPost("execute-products-filter")]
    public async Task<IActionResult> ExecuteProductsFilter([FromBody] FilterGroup filterGroup)
    {
        try
        {
            var (query, parameters) = _filterBuilder.Build(filterGroup);
            var tableName = GetTableName("Products");
            var sql = $"SELECT * FROM {tableName} WHERE {query}";

            var results = await ExecuteQueryAsync<Product>(sql, parameters);

            return Ok(new
            {
                Query = sql,
                Parameters = parameters,
                Results = results,
                Count = results.Count
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message });
        }
    }

    /// <summary>
    /// Executes filter using multiple ORMs for comparison
    /// </summary>
    [HttpPost("execute-filter")]
    public async Task<IActionResult> ExecuteFilter([FromBody] FilterGroup filterGroup)
    {
        try
        {
            var (query, parameters) = _filterBuilder.Build(filterGroup);

            var efResults = await ExecuteWithEntityFrameworkAsync(query, parameters);
            var dapperResults = await ExecuteWithDapperAsync(query, parameters);
            var adoNetResults = await ExecuteWithAdoNetAsync(query, parameters);

            return Ok(new
            {
                Query = query,
                Parameters = parameters,
                Results = new
                {
                    EntityFramework = efResults,
                    Dapper = dapperResults,
                    AdoNet = adoNetResults
                }
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message, StackTrace = ex.StackTrace, InnerException = ex.InnerException?.Message });
        }
    }

    /// <summary>
    /// Executes filter specifically using Entity Framework Core
    /// </summary>
    [HttpPost("execute-efcore-filter")]
    public async Task<IActionResult> ExecuteEFCoreFilter([FromBody] FilterGroup filterGroup)
    {
        try
        {
            var (query, parameters) = _filterBuilder.Build(filterGroup);
            var efResults = await ExecuteWithEntityFrameworkAsync(query, parameters);

            return Ok(new
            {
                Query = query,
                Parameters = parameters,
                Results = efResults,
                Count = efResults.Count,
                ORM = "EntityFramework"
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message, StackTrace = ex.StackTrace, InnerException = ex.InnerException?.Message });
        }
    }

    /// <summary>
    /// Executes filter specifically using Entity Framework Core for Users
    /// </summary>
    [HttpPost("execute-efcore-users")]
    public async Task<IActionResult> ExecuteEFCoreUsers([FromBody] FilterGroup filterGroup)
    {
        try
        {
            var (query, parameters) = _filterBuilder.Build(filterGroup);
            var efResults = await ExecuteWithEntityFrameworkAsync(query, parameters);

            return Ok(new
            {
                Query = query,
                Parameters = parameters,
                Results = efResults,
                Count = efResults.Count,
                ORM = "EntityFramework"
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message, StackTrace = ex.StackTrace, InnerException = ex.InnerException?.Message });
        }
    }

    /// <summary>
    /// Executes join filter specifically using Entity Framework Core
    /// </summary>
    [HttpPost("execute-efcore-join")]
    public async Task<IActionResult> ExecuteEFCoreJoin([FromBody] FilterGroup filterGroup)
    {
        try
        {
            var (query, parameters) = _filterBuilder.Build(filterGroup);
            // For join scenarios, we'll use a simplified approach for now
            var efResults = await ExecuteWithEntityFrameworkAsync(query, parameters);

            return Ok(new
            {
                Query = query,
                Parameters = parameters,
                Results = efResults,
                Count = efResults.Count,
                ORM = "EntityFramework",
                Type = "Join"
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message, StackTrace = ex.StackTrace, InnerException = ex.InnerException?.Message });
        }
    }

    /// <summary>
    /// Executes filter specifically using Dapper
    /// </summary>
    [HttpPost("execute-dapper-filter")]
    public async Task<IActionResult> ExecuteDapperFilter([FromBody] FilterGroup filterGroup)
    {
        try
        {
            var (query, parameters) = _filterBuilder.Build(filterGroup);
            var dapperResults = await ExecuteWithDapperAsync(query, parameters);

            return Ok(new
            {
                Query = query,
                Parameters = parameters,
                Results = dapperResults,
                Count = dapperResults.Count,
                ORM = "Dapper"
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message, StackTrace = ex.StackTrace, InnerException = ex.InnerException?.Message });
        }
    }

    /// <summary>
    /// Executes filter specifically using Dapper for Users
    /// </summary>
    [HttpPost("execute-dapper-users")]
    public async Task<IActionResult> ExecuteDapperUsers([FromBody] FilterGroup filterGroup)
    {
        try
        {
            var (query, parameters) = _filterBuilder.Build(filterGroup);
            var dapperResults = await ExecuteWithDapperAsync(query, parameters);

            return Ok(new
            {
                Query = query,
                Parameters = parameters,
                Results = dapperResults,
                Count = dapperResults.Count,
                ORM = "Dapper"
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message, StackTrace = ex.StackTrace, InnerException = ex.InnerException?.Message });
        }
    }

    /// <summary>
    /// Executes join filter specifically using Dapper
    /// </summary>
    [HttpPost("execute-dapper-join")]
    public async Task<IActionResult> ExecuteDapperJoin([FromBody] FilterGroup filterGroup)
    {
        try
        {
            var (query, parameters) = _filterBuilder.Build(filterGroup);
            var dapperResults = await ExecuteWithDapperAsync(query, parameters);

            return Ok(new
            {
                Query = query,
                Parameters = parameters,
                Results = dapperResults,
                Count = dapperResults.Count,
                ORM = "Dapper",
                Type = "Join"
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message, StackTrace = ex.StackTrace, InnerException = ex.InnerException?.Message });
        }
    }

    /// <summary>
    /// Executes filter specifically using ADO.NET
    /// </summary>
    [HttpPost("execute-adonet-filter")]
    public async Task<IActionResult> ExecuteAdoNetFilter([FromBody] FilterGroup filterGroup)
    {
        try
        {
            var (query, parameters) = _filterBuilder.Build(filterGroup);
            var adoNetResults = await ExecuteWithAdoNetAsync(query, parameters);

            return Ok(new
            {
                Query = query,
                Parameters = parameters,
                Results = adoNetResults,
                Count = adoNetResults.Count,
                ORM = "ADO.NET"
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message, StackTrace = ex.StackTrace, InnerException = ex.InnerException?.Message });
        }
    }

    /// <summary>
    /// Executes filter specifically using ADO.NET for Users
    /// </summary>
    [HttpPost("execute-adonet-users")]
    public async Task<IActionResult> ExecuteAdoNetUsers([FromBody] FilterGroup filterGroup)
    {
        try
        {
            var (query, parameters) = _filterBuilder.Build(filterGroup);
            var adoNetResults = await ExecuteWithAdoNetAsync(query, parameters);

            return Ok(new
            {
                Query = query,
                Parameters = parameters,
                Results = adoNetResults,
                Count = adoNetResults.Count,
                ORM = "ADO.NET"
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message, StackTrace = ex.StackTrace, InnerException = ex.InnerException?.Message });
        }
    }

    /// <summary>
    /// Executes join filter specifically using ADO.NET
    /// </summary>
    [HttpPost("execute-adonet-join")]
    public async Task<IActionResult> ExecuteAdoNetJoin([FromBody] FilterGroup filterGroup)
    {
        try
        {
            var (query, parameters) = _filterBuilder.Build(filterGroup);
            var adoNetResults = await ExecuteWithAdoNetAsync(query, parameters);

            return Ok(new
            {
                Query = query,
                Parameters = parameters,
                Results = adoNetResults,
                Count = adoNetResults.Count,
                ORM = "ADO.NET",
                Type = "Join"
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message, StackTrace = ex.StackTrace, InnerException = ex.InnerException?.Message });
        }
    }

    /// <summary>
    /// Executes join filter for general join scenarios
    /// </summary>
    [HttpPost("execute-join-filter")]
    public async Task<IActionResult> ExecuteJoinFilter([FromBody] FilterGroup filterGroup)
    {
        try
        {
            var (query, parameters) = _filterBuilder.Build(filterGroup);
            // For join scenarios, use Dapper as it handles joins well
            var joinResults = await ExecuteWithDapperAsync(query, parameters);

            return Ok(new
            {
                Query = query,
                Parameters = parameters,
                Results = joinResults,
                Count = joinResults.Count,
                Type = "Join"
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message, StackTrace = ex.StackTrace, InnerException = ex.InnerException?.Message });
        }
    }

    private async Task<List<User>> ExecuteWithEntityFrameworkAsync(string whereClause, object[] parameters)
    {
        // For Entity Framework, we need to use the parameters directly
        var tableName = GetTableName("Users");
        var sql = $"SELECT * FROM {tableName} WHERE {whereClause}";

        // EF Core expects parameters in the format {0}, {1}, etc.
        // But our FilterBuilder generates @p0, @p1, etc.
        // We need to replace the parameter names
        var efSql = sql;
        var provider = _testConfig.GetDatabaseProvider();

        // Simple sequential replacement - replace all parameters in order
        for (int i = 0; i < parameters.Length; i++)
        {
            var oldParam = provider switch
            {
                DatabaseProvider.SqlServer => $"@p{i}",
                DatabaseProvider.PostgreSql => $"${i + 1}",
                DatabaseProvider.MySql => "?",
                _ => $"@p{i}"
            };

            var newParam = $"{{{i}}}";
            efSql = efSql.Replace(oldParam, newParam);
        }

        return await _context.Users.FromSqlRaw(efSql, parameters).ToListAsync();
    }

    private async Task<List<dynamic>> ExecuteWithDapperAsync(string whereClause, object[] parameters)
    {
        var tableName = GetTableName("Users");
        var sql = $"SELECT * FROM {tableName} WHERE {whereClause}";
        using var connection = CreateConnection();
        var provider = _testConfig.GetDatabaseProvider();

        try
        {
            // For MySQL, use DynamicParameters to handle positional parameters
            if (provider == DatabaseProvider.MySql)
            {
                var dynamicParams = new DynamicParameters();
                for (int i = 0; i < parameters.Length; i++)
                {
                    dynamicParams.Add($"p{i}", parameters[i]);
                }
                var mysqlResults = await connection.QueryAsync(sql, dynamicParams);
                return mysqlResults.ToList();
            }

            // For SQL Server and PostgreSQL, create a dictionary of parameters
            var paramDict = ExtractParametersFromSql(sql, parameters);
            var dictResults = await connection.QueryAsync(sql, paramDict);
            return dictResults.ToList();
        }
        catch (Exception ex)
        {
            // Add more context to the error message
            var providerName = provider.ToString();
            var paramInfo = string.Join(", ", parameters.Select((p, i) => $"p{i}={p}"));
            throw new Exception($"Error executing Dapper query for {providerName}. SQL: {sql}, Parameters: [{paramInfo}]. Inner error: {ex.Message}", ex);
        }
    }

    private async Task<List<dynamic>> ExecuteWithAdoNetAsync(string whereClause, object[] parameters)
    {
        var tableName = GetTableName("Users");
        var sql = $"SELECT * FROM {tableName} WHERE {whereClause}";
        using var connection = CreateConnection();
        using var command = connection.CreateCommand();
        command.CommandText = sql;
        
        // Add parameters
        for (int i = 0; i < parameters.Length; i++)
        {
            var parameter = command.CreateParameter();
            parameter.ParameterName = GetParameterName(i);
            parameter.Value = parameters[i] ?? DBNull.Value;
            command.Parameters.Add(parameter);
        }

        if (connection is SqlConnection sqlConn)
        {
            await sqlConn.OpenAsync();
            using var sqlCommand = (SqlCommand)command;
            using var reader = await sqlCommand.ExecuteReaderAsync();
            return await ReadResultsAsync(reader);
        }
        else if (connection is MySqlConnection mysqlConn)
        {
            await mysqlConn.OpenAsync();
            using var mysqlCommand = (MySqlCommand)command;
            using var reader = await mysqlCommand.ExecuteReaderAsync();
            return await ReadResultsAsync(reader);
        }
        else if (connection is NpgsqlConnection npgsqlConn)
        {
            await npgsqlConn.OpenAsync();
            using var npgsqlCommand = (NpgsqlCommand)command;
            using var reader = await npgsqlCommand.ExecuteReaderAsync();
            return await ReadResultsAsync(reader);
        }
        else
        {
            connection.Open();
            using var reader = command.ExecuteReader();
            return ReadResults(reader);
        }
    }

    private async Task<List<dynamic>> ReadResultsAsync(IDataReader reader)
    {
        var results = new List<dynamic>();
        while (await ((DbDataReader)reader).ReadAsync())
        {
            var row = new Dictionary<string, object?>();
            for (int i = 0; i < reader.FieldCount; i++)
            {
                row[reader.GetName(i)] = reader.IsDBNull(i) ? null : reader.GetValue(i);
            }
            results.Add(row);
        }
        return results;
    }

    private List<dynamic> ReadResults(IDataReader reader)
    {
        var results = new List<dynamic>();
        while (reader.Read())
        {
            var row = new Dictionary<string, object?>();
            for (int i = 0; i < reader.FieldCount; i++)
            {
                row[reader.GetName(i)] = reader.IsDBNull(i) ? null : reader.GetValue(i);
            }
            results.Add(row);
        }
        return results;
    }

    private async Task<List<T>> ExecuteQueryAsync<T>(string sql, object[] parameters)
    {
        using var connection = CreateConnection();
        var provider = _testConfig.GetDatabaseProvider();

        try
        {
            // For MySQL, use DynamicParameters to handle positional parameters
            if (provider == DatabaseProvider.MySql)
            {
                var dynamicParams = new DynamicParameters();
                for (int i = 0; i < parameters.Length; i++)
                {
                    dynamicParams.Add($"p{i}", parameters[i]);
                }
                var mysqlResults = await connection.QueryAsync<T>(sql, dynamicParams);
                return mysqlResults.ToList();
            }

            // For PostgreSQL, use DynamicParameters with proper parameter names
            if (provider == DatabaseProvider.PostgreSql)
            {
                var dynamicParams = new DynamicParameters();
                // Find all PostgreSQL parameters in the SQL and replace them
                var dapperSql = sql;
                var pgParamPattern = @"\$(\d+)";
                var matches = Regex.Matches(sql, pgParamPattern);

                // Create a mapping of PostgreSQL parameter indices to actual parameter array indices
                var paramMapping = new Dictionary<int, int>();
                for (int i = 0; i < matches.Count; i++)
                {
                    var match = matches[i];
                    var pgParamIndex = int.Parse(match.Groups[1].Value);
                    if (!paramMapping.ContainsKey(pgParamIndex))
                    {
                        paramMapping[pgParamIndex] = i; // Map to sequential parameter array index
                    }
                }

                // Replace PostgreSQL parameters with Dapper parameters
                foreach (var kvp in paramMapping)
                {
                    var pgParam = $"${kvp.Key}";
                    var dapperParam = $"@p{kvp.Value}";
                    dapperSql = dapperSql.Replace(pgParam, dapperParam);

                    if (kvp.Value < parameters.Length)
                    {
                        dynamicParams.Add($"p{kvp.Value}", parameters[kvp.Value]);
                    }
                }

                var pgResults = await connection.QueryAsync<T>(dapperSql, dynamicParams);
                return pgResults.ToList();
            }

            // For SQL Server, create a dictionary of parameters
            var paramDict = ExtractParametersFromSql(sql, parameters);
            var dictResults = await connection.QueryAsync<T>(sql, paramDict);
            return dictResults.ToList();
        }
        catch (Exception ex)
        {
            // Add more context to the error message
            var providerName = provider.ToString();
            var paramInfo = string.Join(", ", parameters.Select((p, i) => $"p{i}={p}"));
            throw new Exception($"Error executing query for {providerName}. SQL: {sql}, Parameters: [{paramInfo}]. Inner error: {ex.Message}", ex);
        }
    }

    private IDbConnection CreateConnection()
    {
        var provider = _testConfig.GetDatabaseProvider();
        var connectionString = _context.Database.GetConnectionString();
        
        return provider switch
        {
            DatabaseProvider.SqlServer => new SqlConnection(connectionString),
            DatabaseProvider.MySql => new MySqlConnection(connectionString),
            DatabaseProvider.PostgreSql => new NpgsqlConnection(connectionString),
            _ => throw new ArgumentException($"Unsupported provider: {provider}")
        };
    }



    private string GetParameterName(int index)
    {
        var provider = _testConfig.GetDatabaseProvider();
        return provider switch
        {
            DatabaseProvider.SqlServer => $"@p{index}",
            DatabaseProvider.MySql => "?", // MySQL uses positional parameters
            DatabaseProvider.PostgreSql => $"${index + 1}", // PostgreSQL uses 1-based indexing
            _ => $"p{index}"
        };
    }

    private object ExtractParametersFromSql(string sql, object[] parameters)
    {
        var provider = _testConfig.GetDatabaseProvider();

        if (provider == DatabaseProvider.MySql)
        {
            // MySQL uses positional parameters
            return parameters;
        }

        // For SQL Server and PostgreSQL, create a dictionary of parameters
        var paramDict = new Dictionary<string, object>();

        // Extract parameter names from SQL using regex
        var paramPattern = provider switch
        {
            DatabaseProvider.SqlServer => @"@p\d+",
            DatabaseProvider.PostgreSql => @"\$\d+",
            _ => @"@p\d+"
        };

        var matches = Regex.Matches(sql, paramPattern);
        
        // For PostgreSQL, we need to handle the 1-based indexing
        if (provider == DatabaseProvider.PostgreSql)
        {
            for (int i = 0; i < matches.Count && i < parameters.Length; i++)
            {
                var paramName = matches[i].Value;
                // PostgreSQL uses $1, $2, etc. but our parameters array is 0-based
                var paramIndex = int.Parse(paramName.Substring(1)) - 1;
                if (paramIndex >= 0 && paramIndex < parameters.Length)
                {
                    paramDict[paramName] = parameters[paramIndex];
                }
            }
        }
        else
        {
            // For SQL Server, use direct mapping since @p0, @p1 match array indices
            for (int i = 0; i < matches.Count && i < parameters.Length; i++)
            {
                var paramName = matches[i].Value;
                paramDict[paramName] = parameters[i];
            }
        }

        return paramDict;
    }

    private string GetTableName(string tableName)
    {
        var provider = _testConfig.GetDatabaseProvider();
        return provider switch
        {
            DatabaseProvider.PostgreSql => $"\"{tableName}\"", // PostgreSQL needs quoted table names for case sensitivity
            DatabaseProvider.MySql => $"`{tableName}`", // MySQL uses backticks
            DatabaseProvider.SqlServer => $"[{tableName}]", // SQL Server uses square brackets
            _ => tableName
        };
    }
}
