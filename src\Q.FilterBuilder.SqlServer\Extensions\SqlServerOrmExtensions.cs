using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace Q.FilterBuilder.SqlServer.Extensions;

/// <summary>
/// SQL Server specific parameter and query transformation extensions
/// </summary>
public static class SqlServerOrmExtensions
{
    /// <summary>
    /// Convert SQL Server query to EF Core raw query format
    /// </summary>
    /// <param name="whereClause"></param>
    /// <returns></returns>
    public static string ToEfRawQueryFormat(this string whereClause)
    {
        var efQuery = whereClause;
        // use regex to find all @p0, @p1, etc.
        var matches = Regex.Matches(efQuery, @"@p\d+");

        // foreach matches and replace
        foreach (Match match in matches)
        {
            var index = int.Parse(match.Value.Substring(2));
            efQuery = efQuery.Replace(match.Value, $"{{{index}}}");
        }

        return efQuery;
    }

    /// <summary>
    /// Convert SQL Server query parameters for Dapper
    /// </summary>
    /// <param name="parameters"></param>
    /// <returns></returns>
    public static Dictionary<string, object> ToDapperParameters(this object[] parameters)
    {
        var paramDict = new Dictionary<string, object>();
        for (var i = 0; i < parameters.Length; i++)
        {
            paramDict[$"@p{i}"] = parameters[i];
        }
        return paramDict;
    }
}
