using System.Data;
using System.Data.Common;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Dapper;

namespace Q.FilterBuilder.SqlServer.Extensions;

/// <summary>
/// SQL Server specific ORM integration extensions
/// </summary>
public static class SqlServerOrmExtensions
{
    /// <summary>
    /// Execute query using Entity Framework Core with SQL Server specific parameter handling
    /// </summary>
    public static async Task<List<T>> ExecuteWithEntityFrameworkAsync<T>(
        this DbContext context, 
        string whereClause, 
        object[] parameters) where T : class
    {
        var tableName = GetTableName<T>();
        var sql = $"SELECT * FROM [{tableName}] WHERE {whereClause}";
        
        // Convert parameters for EF Core (uses {0}, {1}, etc.)
        var efSql = ConvertParametersForEntityFramework(sql, parameters.Length);
        
        return await context.Set<T>().FromSqlRaw(efSql, parameters).ToListAsync();
    }

    /// <summary>
    /// Execute query using Dapper with SQL Server specific parameter handling
    /// </summary>
    public static async Task<List<T>> ExecuteWithDapperAsync<T>(
        string connectionString,
        string whereClause, 
        object[] parameters)
    {
        var tableName = GetTableName<T>();
        var sql = $"SELECT * FROM [{tableName}] WHERE {whereClause}";
        
        using var connection = new SqlConnection(connectionString);
        var paramDict = ConvertParametersForDapper(sql, parameters);
        
        var results = await connection.QueryAsync<T>(sql, paramDict);
        return results.ToList();
    }

    /// <summary>
    /// Execute query using ADO.NET with SQL Server specific parameter handling
    /// </summary>
    public static async Task<List<dynamic>> ExecuteWithAdoNetAsync(
        string connectionString,
        string whereClause, 
        object[] parameters)
    {
        var tableName = GetTableName<dynamic>();
        var sql = $"SELECT * FROM [Users] WHERE {whereClause}";
        
        using var connection = new SqlConnection(connectionString);
        using var command = connection.CreateCommand();
        command.CommandText = sql;
        
        // Add parameters with SQL Server naming (@p0, @p1, etc.)
        for (int i = 0; i < parameters.Length; i++)
        {
            var parameter = command.CreateParameter();
            parameter.ParameterName = $"@p{i}";
            parameter.Value = parameters[i] ?? DBNull.Value;
            command.Parameters.Add(parameter);
        }

        await connection.OpenAsync();
        using var reader = await command.ExecuteReaderAsync();
        return await ReadResultsAsync(reader);
    }

    /// <summary>
    /// Get expected SQL query string for SQL Server
    /// </summary>
    public static string GetExpectedSqlQuery(string whereClause, string tableName = "Users")
    {
        return $"SELECT * FROM [{tableName}] WHERE {whereClause}";
    }

    /// <summary>
    /// Validate SQL Server parameter format
    /// </summary>
    public static bool ValidateParameterFormat(string sql)
    {
        // SQL Server uses @p0, @p1, etc.
        return sql.Contains("@p");
    }

    private static string GetTableName<T>()
    {
        var type = typeof(T);
        if (type == typeof(dynamic))
            return "Users"; // Default table for dynamic queries
        
        var tableName = type.Name;
        if (tableName.EndsWith("y"))
            tableName = tableName.Substring(0, tableName.Length - 1) + "ies";
        else if (!tableName.EndsWith("s"))
            tableName += "s";
        
        return tableName;
    }

    private static string ConvertParametersForEntityFramework(string sql, int parameterCount)
    {
        var efSql = sql;
        for (int i = 0; i < parameterCount; i++)
        {
            efSql = efSql.Replace($"@p{i}", $"{{{i}}}");
        }
        return efSql;
    }

    private static Dictionary<string, object> ConvertParametersForDapper(string sql, object[] parameters)
    {
        var paramDict = new Dictionary<string, object>();
        for (int i = 0; i < parameters.Length; i++)
        {
            paramDict[$"@p{i}"] = parameters[i];
        }
        return paramDict;
    }

    private static async Task<List<dynamic>> ReadResultsAsync(DbDataReader reader)
    {
        var results = new List<dynamic>();
        while (await reader.ReadAsync())
        {
            var row = new Dictionary<string, object?>();
            for (int i = 0; i < reader.FieldCount; i++)
            {
                row[reader.GetName(i)] = reader.IsDBNull(i) ? null : reader.GetValue(i);
            }
            results.Add(row);
        }
        return results;
    }
}
